请你创建一个JSON格式的设计系统档案，从提供的截图或者提供的参考链接中提取视觉和交互数据。这个JSON将用于Cursor等AI编程工具，为其提供统一风格复现的上下文信息。

请按以下结构输出，避免包含具体图片或参考链接的内容：

{
  "designSystem": {
    "visual": {
      "colorPalette": {
        "primary": "主色调（通常是黑白灰）(具体看用户需求)",
        "accent": "强调色（品牌色，用于CTA和重点元素）",
        "gradients": "微妙的同色系渐变，避免彩虹色",
        "transparency": "半透明效果和玻璃拟态"
      },
      "typography": {
        "fontFamily": "现代无衬线字体（Inter、SF Pro等）",
        "hierarchy": "清晰的字号层级（48px/32px/18px/16px/14px）",
        "fontWeight": "字重使用（300/400/500/600/700/800）",
        "lineHeight": "舒适的行高（1.4-1.6）",
        "letterSpacing": "字母间距（-0.02em用于大标题）"
      },
      "spacing": {
        "system": "8px基础网格系统（8/16/24/32/48/64/80/120px）",
        "whitespace": "大量留白营造呼吸感",
        "contentWidth": "最大宽度限制（1200px-1400px）",
        "sectionPadding": "区域间距（80px-120px）"
      },
      "components": {
        "buttons": "简洁的圆角按钮，主要/次要层次分明",
        "cards": "微妙阴影或边框，玻璃拟态效果",
        "forms": "清晰的输入框设计，聚焦状态明显",
        "navigation": "简洁的导航，固定或隐藏式"
      }
    },
    "interactions": {
      "scrollBehavior": {
        "smoothScrolling": "CSS scroll-behavior: smooth + JS优化",
        "parallaxEffects": "轻微的视差效果，不过度",
        "scrollTriggers": "元素进入视口时的淡入动画",
        "progressIndicators": "滚动进度条或其他进度指示"
      },
      "animations": {
        "fadeInOut": {
          "trigger": "滚动时元素依次淡入淡出",
          "timing": "0.6s-0.8s duration，cubic-bezier(0.4, 0, 0.2, 1)",
          "stagger": "元素间隔100-200ms依次出现",
          "direction": "从下方30px淡入，或从左右滑入"
        },
        "microInteractions": {
          "hover": "按钮轻微上移(-2px)，颜色渐变",
          "click": "涟漪效果或轻微缩放",
          "loading": "优雅的加载动画，避免突兀"
        },
        "pageTransitions": "页面间切换的流畅过渡"
      },
      "performance": {
        "optimization": "transform和opacity优化，避免layout重排",
        "hardwareAcceleration": "will-change和transform3d",
        "debounceThrottle": "滚动事件优化",
        "lazyLoading": "图片和内容的懒加载"
      }
    },
    "aiIndustryStandards": {
      "minimalism": {
        "philosophy": "极简主义，内容为王",
        "whitespace": "大量留白，让内容呼吸",
        "hierarchy": "清晰的信息层次，突出重点",
        "reduction": "去除一切不必要的装饰元素"
      },
      "professionalism": {
        "colorScheme": "黑白灰主调 + 单一品牌强调色",
        "typography": "专业级字体选择和排版",
        "consistency": "整站一致的设计语言",
        "accessibility": "优秀的可访问性和对比度"
      },
      "modernInteractions": {
        "smoothness": "60fps流畅动画，无卡顿",
        "responsiveness": "即时的用户反馈",
        "intuitive": "直观的交互逻辑",
        "seamless": "无缝的用户体验流程"
      }
    },
    "technicalImplementation": {
      "cssFeatures": [
        "backdrop-filter: blur() 玻璃拟态",
        "transform3d() 硬件加速",
        "cubic-bezier() 自然缓动",
        "CSS Grid/Flexbox 现代布局",
        "CSS自定义属性管理主题"
      ],
      "jsPatterns": [
        "IntersectionObserver 滚动触发",
        "requestAnimationFrame 性能优化",
        "防抖节流优化滚动事件",
        "模块化的类结构",
        "事件委托减少监听器"
      ],
      "bestPractices": [
        "移动端优先的响应式设计",
        "渐进增强的交互体验",
        "语义化HTML结构",
        "优化的资源加载策略"
      ]
    }
  }
}

重点关注AI行业网站的特征：
1. **极简美学**: 像Cursor、Warp那样的简洁设计，大量留白（不知道可以搜）
2. **专业配色**: 黑白灰主调，单一品牌色强调
3. **丝滑交互**: 60fps动画，元素滚动时的淡入淡出效果
4. **现代字体**: Inter等专业字体，清晰的层次
5. **玻璃拟态**: 半透明背景，微妙的模糊效果
6. **微交互**: 按钮悬停、点击的精致反馈
7. **内容导向**: 设计服务于功能，不喧宾夺主
8. **一致性**: 整站统一的设计语言和交互模式

确保描述能让AI理解并复现这种现代化、专业级的设计风格。